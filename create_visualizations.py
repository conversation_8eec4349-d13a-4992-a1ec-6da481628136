"""
Module: create_visualizations.py
Description: Generate comprehensive visualizations for FOMHS rankings presentation
Author: Dr. <PERSON>, Symbiosis International (Deemed University)
Created: 2025-01-23
Last Modified: 2025-01-23

Dependencies:
- matplotlib
- seaborn
- pandas
- numpy
"""

import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
import os
from src.data_analysis import RankingsAnalyzer
from src.visualization import RankingsVisualizer

def create_all_visualizations():
    """Create all visualizations for the presentation."""
    
    # Initialize analyzer and visualizer
    analyzer = RankingsAnalyzer()
    visualizer = RankingsVisualizer()
    
    # Load and analyze data
    print("Loading data for visualizations...")
    analyzer.load_all_data()
    nirf_results = analyzer.analyze_nirf_performance()
    qs_results = analyzer.analyze_qs_performance()
    the_results = analyzer.analyze_the_performance()
    
    # Create output directory
    os.makedirs('output/figures', exist_ok=True)
    
    # 1. NIRF Top Performers Chart
    print("Creating NIRF top performers chart...")
    if nirf_results and 'top_performers' in nirf_results:
        fig, ax = visualizer.create_nirf_top_performers_chart(
            nirf_results['top_performers'], 
            save_path='output/figures/nirf_top_performers.png'
        )
        plt.close(fig)
    
    # 2. QS Subject Rankings Comparison
    print("Creating QS subject rankings comparison...")
    if qs_results:
        fig, ax = visualizer.create_qs_subject_comparison(
            qs_results,
            save_path='output/figures/qs_subject_comparison.png'
        )
        if fig:
            plt.close(fig)
    
    # 3. THE Performance Heatmap
    print("Creating THE performance heatmap...")
    if the_results:
        fig, ax = visualizer.create_the_performance_heatmap(
            the_results,
            save_path='output/figures/the_performance_heatmap.png'
        )
        if fig:
            plt.close(fig)
    
    # 4. Parameter Importance Analysis
    print("Creating parameter importance chart...")
    create_parameter_importance_chart(nirf_results)
    
    # 5. Ranking Methodology Comparison
    print("Creating ranking methodology comparison...")
    create_methodology_comparison_chart()
    
    # 6. SIU Performance Radar Chart
    print("Creating SIU performance radar chart...")
    create_siu_performance_radar(the_results)
    
    # 7. Investment vs Impact Analysis
    print("Creating investment impact analysis...")
    create_investment_impact_chart()
    
    # 8. Timeline Roadmap Visualization
    print("Creating timeline roadmap...")
    create_timeline_roadmap()
    
    print("All visualizations created successfully!")

def create_parameter_importance_chart(nirf_results):
    """Create parameter importance chart for NIRF."""
    if not nirf_results or 'parameter_importance' not in nirf_results:
        return
    
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # Data for parameter importance
    params = list(nirf_results['parameter_importance'].keys())
    importance = list(nirf_results['parameter_importance'].values())
    
    # Create horizontal bar chart
    colors = ['#DC143C', '#1E3A8A', '#F59E0B', '#10B981', '#F97316']
    bars = ax.barh(params, importance, color=colors[:len(params)])
    
    # Customize chart
    ax.set_xlabel('Correlation with Overall Score', fontweight='bold', fontsize=14)
    ax.set_title('NIRF Parameter Importance Analysis\nCorrelation with Overall Ranking Score', 
                fontsize=16, fontweight='bold', pad=20)
    
    # Add value labels
    for bar, value in zip(bars, importance):
        ax.text(bar.get_width() + 0.01, bar.get_y() + bar.get_height()/2, 
               f'{value:.3f}', va='center', fontweight='bold')
    
    ax.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('output/figures/nirf_parameter_importance.png', dpi=300, bbox_inches='tight')
    plt.close(fig)

def create_methodology_comparison_chart():
    """Create ranking methodology comparison chart."""
    fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(18, 8))
    
    # NIRF Methodology
    nirf_params = ['Teaching-Learning\nResources (30%)', 'Research &\nProfessional Practice (40%)', 
                   'Graduation\nOutcomes (30%)', 'Outreach &\nInclusivity (20%)', 'Perception (10%)']
    nirf_weights = [30, 40, 30, 20, 10]
    
    ax1.pie(nirf_weights, labels=nirf_params, autopct='%1.0f%%', startangle=90,
           colors=['#DC143C', '#1E3A8A', '#F59E0B', '#10B981', '#F97316'])
    ax1.set_title('NIRF Methodology', fontsize=14, fontweight='bold')
    
    # QS Methodology
    qs_params = ['Academic\nReputation (30%)', 'Employer\nReputation (20%)', 
                'Citations per\nFaculty (20%)', 'H-Index (20%)', 'International\nResearch Network (10%)']
    qs_weights = [30, 20, 20, 20, 10]
    
    ax2.pie(qs_weights, labels=qs_params, autopct='%1.0f%%', startangle=90,
           colors=['#DC143C', '#1E3A8A', '#F59E0B', '#10B981', '#F97316'])
    ax2.set_title('QS Methodology', fontsize=14, fontweight='bold')
    
    # THE Methodology
    the_params = ['Research\nQuality (30%)', 'Research\nEnvironment (29%)', 
                 'Teaching (28.5%)', 'International\nOutlook (7.5%)', 'Industry (4%)']
    the_weights = [30, 29, 28.5, 7.5, 4]
    
    ax3.pie(the_weights, labels=the_params, autopct='%1.1f%%', startangle=90,
           colors=['#DC143C', '#1E3A8A', '#F59E0B', '#10B981', '#F97316'])
    ax3.set_title('THE Methodology', fontsize=14, fontweight='bold')
    
    plt.suptitle('Ranking Methodology Comparison: NIRF vs QS vs THE', 
                fontsize=18, fontweight='bold', y=0.98)
    plt.tight_layout()
    plt.savefig('output/figures/methodology_comparison.png', dpi=300, bbox_inches='tight')
    plt.close(fig)

def create_siu_performance_radar(the_results):
    """Create SIU performance radar chart."""
    if not the_results or 'Medical & Health' not in the_results:
        return
    
    siu_data = the_results['Medical & Health'].get('siu_position')
    if siu_data is None or len(siu_data) == 0:
        return
    
    # Parameters and values
    parameters = ['Research Quality', 'Industry Engagement', 'International Outlook', 
                 'Research Environment', 'Teaching Excellence']
    
    siu_values = [
        float(siu_data['Research_Quality'].iloc[0]),
        float(siu_data['Industry'].iloc[0]),
        float(siu_data['International_Outlook'].iloc[0]),
        float(siu_data['Research_Enviroment'].iloc[0]),
        float(siu_data['Teaching'].iloc[0])
    ]
    
    # Target values (aspirational)
    target_values = [60, 50, 60, 40, 50]
    
    # Create radar chart
    fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
    
    # Number of parameters
    N = len(parameters)
    angles = [n / float(N) * 2 * np.pi for n in range(N)]
    angles += angles[:1]
    
    # Add values to complete the circle
    siu_values += siu_values[:1]
    target_values += target_values[:1]
    
    # Plot
    ax.plot(angles, siu_values, 'o-', linewidth=3, label='SIU Current', color='#DC143C')
    ax.fill(angles, siu_values, alpha=0.25, color='#DC143C')
    
    ax.plot(angles, target_values, 'o-', linewidth=3, label='Target 2027', color='#1E3A8A')
    ax.fill(angles, target_values, alpha=0.25, color='#1E3A8A')
    
    # Customize
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(parameters, fontsize=12)
    ax.set_ylim(0, 80)
    ax.set_yticks([20, 40, 60, 80])
    ax.set_yticklabels(['20', '40', '60', '80'], fontsize=10)
    ax.grid(True)
    
    plt.title('SIU Performance vs 2027 Targets\nTHE Medical & Health Parameters', 
             size=16, fontweight='bold', pad=30)
    plt.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    
    plt.tight_layout()
    plt.savefig('output/figures/siu_performance_radar.png', dpi=300, bbox_inches='tight')
    plt.close(fig)

def create_investment_impact_chart():
    """Create investment vs impact analysis chart."""
    fig, ax = plt.subplots(figsize=(14, 10))
    
    # Investment areas and their impact potential
    investments = ['Faculty\nRecruitment', 'Research\nInfrastructure', 'International\nPartnerships', 
                  'Industry\nCollaborations', 'Student\nSupport', 'Technology\nUpgrade', 
                  'Marketing &\nBranding', 'Quality\nAssurance']
    
    investment_cost = [50, 30, 10, 15, 15, 25, 8, 12]  # in crores
    ranking_impact = [85, 70, 75, 60, 45, 55, 40, 50]  # impact score out of 100
    
    # Create bubble chart
    colors = ['#DC143C', '#1E3A8A', '#F59E0B', '#10B981', '#F97316', '#8B5CF6', '#EF4444', '#06B6D4']
    
    scatter = ax.scatter(investment_cost, ranking_impact, s=[c*20 for c in investment_cost], 
                        c=colors, alpha=0.7, edgecolors='black', linewidth=2)
    
    # Add labels
    for i, txt in enumerate(investments):
        ax.annotate(txt, (investment_cost[i], ranking_impact[i]), 
                   xytext=(5, 5), textcoords='offset points', fontsize=10, fontweight='bold')
    
    # Customize chart
    ax.set_xlabel('Investment Required (₹ Crores)', fontweight='bold', fontsize=14)
    ax.set_ylabel('Ranking Impact Potential (Score out of 100)', fontweight='bold', fontsize=14)
    ax.set_title('Investment vs Ranking Impact Analysis\nBubble Size = Investment Amount', 
                fontsize=16, fontweight='bold', pad=20)
    
    # Add quadrant lines
    ax.axhline(y=60, color='gray', linestyle='--', alpha=0.5)
    ax.axvline(x=20, color='gray', linestyle='--', alpha=0.5)
    
    # Add quadrant labels
    ax.text(45, 90, 'High Impact\nHigh Investment', fontsize=12, fontweight='bold', 
           bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgreen', alpha=0.7))
    ax.text(5, 90, 'High Impact\nLow Investment', fontsize=12, fontweight='bold',
           bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.7))
    
    ax.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('output/figures/investment_impact_analysis.png', dpi=300, bbox_inches='tight')
    plt.close(fig)

def create_timeline_roadmap():
    """Create timeline roadmap visualization."""
    fig, ax = plt.subplots(figsize=(16, 10))
    
    # Timeline data
    years = ['2025', '2026', '2027']
    milestones = [
        ['Rankings Task Force', 'Faculty Recruitment', 'International Partnerships', 'Research Incentives'],
        ['200+ Publications', 'Industry Partnerships', 'Student Exchange', 'Research Funding'],
        ['NIRF Top 30', 'QS Top 400', 'THE Top 400', 'Global Recognition']
    ]
    
    colors = ['#DC143C', '#1E3A8A', '#F59E0B']
    
    # Create timeline
    for i, (year, items, color) in enumerate(zip(years, milestones, colors)):
        # Year marker
        ax.scatter(i, 0, s=500, c=color, zorder=3)
        ax.text(i, -0.3, year, ha='center', va='top', fontsize=16, fontweight='bold')
        
        # Milestones
        for j, item in enumerate(items):
            y_pos = 0.5 + j * 0.4
            ax.text(i, y_pos, f"• {item}", ha='center', va='center', fontsize=11,
                   bbox=dict(boxstyle="round,pad=0.3", facecolor=color, alpha=0.3))
    
    # Connect timeline
    ax.plot(range(len(years)), [0]*len(years), 'k-', linewidth=3, zorder=1)
    
    # Customize
    ax.set_xlim(-0.5, len(years)-0.5)
    ax.set_ylim(-0.5, 2.5)
    ax.set_title('FOMHS Rankings Excellence Roadmap (2025-2027)', 
                fontsize=18, fontweight='bold', pad=30)
    
    # Remove axes
    ax.set_xticks([])
    ax.set_yticks([])
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['bottom'].set_visible(False)
    ax.spines['left'].set_visible(False)
    
    plt.tight_layout()
    plt.savefig('output/figures/timeline_roadmap.png', dpi=300, bbox_inches='tight')
    plt.close(fig)

if __name__ == "__main__":
    create_all_visualizations()
    print("All visualizations have been created and saved to output/figures/")
