"""
Module: visualization.py
Description: Professional visualization creation for rankings analysis presentation
Author: Dr. <PERSON>, Symbiosis International (Deemed University)
Created: 2025-01-23
Last Modified: 2025-01-23

Dependencies:
- matplotlib
- seaborn
- pandas
- numpy
"""

import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
import os
from matplotlib.patches import Rectangle
import matplotlib.patches as mpatches

class RankingsVisualizer:
    """
    Professional visualization creator for rankings analysis.
    
    Creates publication-quality charts and graphs using Symbiosis brand colors
    for comprehensive rankings analysis presentation.
    """
    
    def __init__(self):
        """Initialize visualizer with Symbiosis brand styling."""
        self.setup_styling()
        self.output_dir = 'output/figures'
        os.makedirs(self.output_dir, exist_ok=True)
        
    def setup_styling(self):
        """Set up professional styling with Symbiosis brand colors."""
        # Symbiosis brand colors
        self.colors = {
            'primary_red': '#DC143C',
            'secondary_blue': '#1E3A8A',
            'accent_gold': '#F59E0B',
            'neutral_gray': '#6B7280',
            'light_gray': '#F3F4F6',
            'success_green': '#10B981',
            'warning_orange': '#F97316',
            'text_dark': '#1F2937'
        }
        
        # Set global matplotlib parameters
        plt.style.use('seaborn-v0_8-whitegrid')
        plt.rcParams.update({
            'font.family': 'Arial',
            'font.size': 12,
            'axes.titlesize': 16,
            'axes.titleweight': 'bold',
            'axes.labelsize': 14,
            'xtick.labelsize': 11,
            'ytick.labelsize': 11,
            'legend.fontsize': 11,
            'figure.titlesize': 18,
            'figure.titleweight': 'bold',
            'axes.spines.top': False,
            'axes.spines.right': False,
            'axes.grid': True,
            'grid.alpha': 0.3
        })
        
    def create_nirf_top_performers_chart(self, nirf_data, save_path=None):
        """
        Create a horizontal bar chart of top NIRF performers.
        
        Parameters
        ----------
        nirf_data : DataFrame
            NIRF ranking data
        save_path : str, optional
            Path to save the chart
            
        Returns
        -------
        tuple
            Figure and axes objects
        """
        fig, ax = plt.subplots(figsize=(14, 10))
        
        # Get top 15 performers
        top_15 = nirf_data.head(15).copy()
        
        # Create horizontal bar chart
        bars = ax.barh(range(len(top_15)), top_15['Score'], 
                      color=self.colors['primary_red'], alpha=0.8)
        
        # Customize the chart
        ax.set_yticks(range(len(top_15)))
        ax.set_yticklabels([name[:40] + '...' if len(name) > 40 else name 
                           for name in top_15['Name_of_University']], fontsize=10)
        ax.set_xlabel('NIRF Score', fontweight='bold')
        ax.set_title('NIRF 2024 Medical Category - Top 15 Performers', 
                    fontsize=18, fontweight='bold', pad=20)
        
        # Add score labels on bars
        for i, (bar, score) in enumerate(zip(bars, top_15['Score'])):
            ax.text(bar.get_width() + 0.5, bar.get_y() + bar.get_height()/2, 
                   f'{score:.1f}', va='center', fontweight='bold')
        
        # Highlight SIU if present (though it's not in top 15)
        ax.grid(True, alpha=0.3)
        ax.set_xlim(0, max(top_15['Score']) * 1.1)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig, ax
    
    def create_ranking_parameters_radar(self, institution_data, benchmark_data, save_path=None):
        """
        Create a radar chart comparing institution parameters with benchmarks.
        
        Parameters
        ----------
        institution_data : dict
            Institution parameter scores
        benchmark_data : dict
            Benchmark parameter scores
        save_path : str, optional
            Path to save the chart
            
        Returns
        -------
        tuple
            Figure and axes objects
        """
        # Parameters for radar chart
        parameters = list(institution_data.keys())
        values_inst = list(institution_data.values())
        values_bench = list(benchmark_data.values())
        
        # Number of parameters
        N = len(parameters)
        
        # Compute angle for each parameter
        angles = [n / float(N) * 2 * np.pi for n in range(N)]
        angles += angles[:1]  # Complete the circle
        
        # Add first value to end to close the radar chart
        values_inst += values_inst[:1]
        values_bench += values_bench[:1]
        
        # Create the plot
        fig, ax = plt.subplots(figsize=(12, 12), subplot_kw=dict(projection='polar'))
        
        # Plot institution data
        ax.plot(angles, values_inst, 'o-', linewidth=3, 
               label='SIU Performance', color=self.colors['primary_red'])
        ax.fill(angles, values_inst, alpha=0.25, color=self.colors['primary_red'])
        
        # Plot benchmark data
        ax.plot(angles, values_bench, 'o-', linewidth=3, 
               label='Top Performer Average', color=self.colors['secondary_blue'])
        ax.fill(angles, values_bench, alpha=0.25, color=self.colors['secondary_blue'])
        
        # Add parameter labels
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(parameters, fontsize=12)
        
        # Set y-axis limits
        ax.set_ylim(0, 100)
        ax.set_yticks([20, 40, 60, 80, 100])
        ax.set_yticklabels(['20', '40', '60', '80', '100'], fontsize=10)
        ax.grid(True)
        
        # Add title and legend
        plt.title('Performance Comparison: Key Ranking Parameters', 
                 size=16, fontweight='bold', pad=30)
        plt.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig, ax
    
    def create_qs_subject_comparison(self, qs_data, save_path=None):
        """
        Create a comparison chart of QS subject rankings for Indian institutions.
        
        Parameters
        ----------
        qs_data : dict
            QS ranking data by subject
        save_path : str, optional
            Path to save the chart
            
        Returns
        -------
        tuple
            Figure and axes objects
        """
        fig, ax = plt.subplots(figsize=(16, 10))
        
        # Prepare data for visualization
        subjects = []
        institutions = []
        ranks = []
        
        for subject, data in qs_data.items():
            if 'indian_top_5' in data and data['indian_top_5'] is not None:
                indian_top = data['indian_top_5']
                for _, row in indian_top.iterrows():
                    subjects.append(subject)
                    institutions.append(row['Institution_Name'][:30] + '...' 
                                      if len(row['Institution_Name']) > 30 
                                      else row['Institution_Name'])
                    ranks.append(row['Rank_2025'])
        
        if not subjects:
            return None, None
        
        # Create DataFrame for easier plotting
        plot_data = pd.DataFrame({
            'Subject': subjects,
            'Institution': institutions,
            'Rank': ranks
        })
        
        # Create grouped bar chart
        subjects_unique = plot_data['Subject'].unique()
        x_pos = np.arange(len(subjects_unique))
        
        # Get top 3 institutions per subject
        colors_list = [self.colors['primary_red'], self.colors['secondary_blue'], 
                      self.colors['accent_gold'], self.colors['success_green'], 
                      self.colors['warning_orange']]
        
        for i, subject in enumerate(subjects_unique):
            subject_data = plot_data[plot_data['Subject'] == subject].head(3)
            for j, (_, row) in enumerate(subject_data.iterrows()):
                ax.bar(i + j*0.25, row['Rank'], width=0.2, 
                      color=colors_list[j % len(colors_list)], 
                      alpha=0.8, label=row['Institution'] if i == 0 else "")
        
        ax.set_xlabel('QS Subject Areas', fontweight='bold')
        ax.set_ylabel('QS Rank (Lower is Better)', fontweight='bold')
        ax.set_title('QS 2025 Subject Rankings - Top Indian Institutions', 
                    fontsize=16, fontweight='bold', pad=20)
        ax.set_xticks(x_pos + 0.25)
        ax.set_xticklabels(subjects_unique, rotation=45, ha='right')
        
        # Invert y-axis since lower rank is better
        ax.invert_yaxis()
        
        # Add legend
        handles, labels = ax.get_legend_handles_labels()
        unique_labels = []
        unique_handles = []
        for handle, label in zip(handles, labels):
            if label not in unique_labels:
                unique_labels.append(label)
                unique_handles.append(handle)
        
        ax.legend(unique_handles[:5], unique_labels[:5], 
                 bbox_to_anchor=(1.05, 1), loc='upper left')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig, ax
    
    def create_the_performance_heatmap(self, the_data, save_path=None):
        """
        Create a heatmap showing THE ranking parameters performance.
        
        Parameters
        ----------
        the_data : dict
            THE ranking data
        save_path : str, optional
            Path to save the chart
            
        Returns
        -------
        tuple
            Figure and axes objects
        """
        # Prepare data for heatmap
        institutions = []
        parameters = ['Research_Quality', 'Industry', 'International_Outlook', 
                     'Research_Enviroment', 'Teaching']
        
        heatmap_data = []
        
        for subject, data in the_data.items():
            if 'top_10' in data and data['top_10'] is not None:
                top_data = data['top_10'].head(5)  # Top 5 per subject
                for _, row in top_data.iterrows():
                    inst_name = row['Institution_Name'][:25] + '...' if len(row['Institution_Name']) > 25 else row['Institution_Name']
                    institutions.append(f"{inst_name} ({subject})")
                    
                    param_values = []
                    for param in parameters:
                        if param in row and pd.notna(row[param]):
                            param_values.append(float(row[param]))
                        else:
                            param_values.append(0)
                    heatmap_data.append(param_values)
        
        if not heatmap_data:
            return None, None
        
        # Create heatmap
        fig, ax = plt.subplots(figsize=(12, max(8, len(institutions) * 0.4)))
        
        heatmap_df = pd.DataFrame(heatmap_data, 
                                 index=institutions, 
                                 columns=parameters)
        
        sns.heatmap(heatmap_df, annot=True, cmap='RdYlBu_r', 
                   center=50, fmt='.1f', cbar_kws={'label': 'Score'},
                   ax=ax)
        
        ax.set_title('THE 2025 Subject Rankings - Parameter Performance Heatmap', 
                    fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('Ranking Parameters', fontweight='bold')
        ax.set_ylabel('Institutions by Subject', fontweight='bold')
        
        plt.xticks(rotation=45, ha='right')
        plt.yticks(rotation=0)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig, ax
    
    def create_improvement_roadmap_chart(self, current_scores, target_scores, parameters, save_path=None):
        """
        Create a roadmap chart showing improvement targets.
        
        Parameters
        ----------
        current_scores : list
            Current parameter scores
        target_scores : list
            Target parameter scores
        parameters : list
            Parameter names
        save_path : str, optional
            Path to save the chart
            
        Returns
        -------
        tuple
            Figure and axes objects
        """
        fig, ax = plt.subplots(figsize=(14, 8))
        
        x_pos = np.arange(len(parameters))
        width = 0.35
        
        # Create bars
        bars1 = ax.bar(x_pos - width/2, current_scores, width, 
                      label='Current Performance', color=self.colors['neutral_gray'], alpha=0.7)
        bars2 = ax.bar(x_pos + width/2, target_scores, width, 
                      label='Target Performance', color=self.colors['primary_red'], alpha=0.8)
        
        # Add improvement arrows
        for i, (current, target) in enumerate(zip(current_scores, target_scores)):
            if target > current:
                ax.annotate('', xy=(i, target), xytext=(i, current),
                           arrowprops=dict(arrowstyle='->', color=self.colors['success_green'], 
                                         lw=2, alpha=0.8))
        
        # Customize chart
        ax.set_xlabel('Ranking Parameters', fontweight='bold')
        ax.set_ylabel('Score', fontweight='bold')
        ax.set_title('Strategic Improvement Roadmap - Target vs Current Performance', 
                    fontsize=16, fontweight='bold', pad=20)
        ax.set_xticks(x_pos)
        ax.set_xticklabels(parameters, rotation=45, ha='right')
        ax.legend()
        
        # Add value labels on bars
        for bars in [bars1, bars2]:
            for bar in bars:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                       f'{height:.1f}', ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig, ax

if __name__ == "__main__":
    # Test visualization creation
    visualizer = RankingsVisualizer()
    print("Visualization module initialized successfully!")
    print(f"Output directory: {visualizer.output_dir}")
    print(f"Brand colors: {visualizer.colors}")
