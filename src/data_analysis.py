"""
Module: data_analysis.py
Description: Comprehensive analysis of NIRF 2024, QS 2025, and THE 2025 ranking data
Author: Dr. <PERSON>, Symbiosis International (Deemed University)
Created: 2025-01-23
Last Modified: 2025-01-23

Dependencies:
- pandas
- numpy
- matplotlib
- seaborn
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

class RankingsAnalyzer:
    """
    Comprehensive rankings analysis for FOMHS strategic planning.
    
    This class provides methods to analyze NIRF, QS, and THE ranking data
    to generate insights for improving SIU's position in global rankings.
    """
    
    def __init__(self):
        """Initialize the analyzer with data loading capabilities."""
        self.nirf_data = None
        self.qs_data = {}
        self.the_data = {}
        self.analysis_results = {}
        
    def load_all_data(self):
        """
        Load all ranking datasets from CSV files.
        
        Returns
        -------
        dict
            Dictionary containing loaded datasets and basic statistics
        """
        try:
            # Load NIRF 2024 Medical Category Rankings
            self.nirf_data = pd.read_csv('NIRF 2024 Medical Category Rankings.csv')
            print(f"NIRF Data loaded: {len(self.nirf_data)} institutions")
            
            # Load QS Subject Rankings 2025
            qs_files = [
                '2025 QS WUR by Subject Overall in Life Science and Medicine.csv',
                '2025 QS WUR subject rankings Medicine.csv',
                '2025 QS WUR subject rankings Nursing.csv',
                '2025 QS WUR subject rankings Biological Sciences.csv',
                '2025 QS WUR subject rankings Anatomy.csv',
                '2025 QS WUR subject rankings Chemistry.csv',
                '2025 QS WUR subject rankings Dentistry.csv',
                '2025 QS WUR subject rankings Pharmacy & Pharmacology.csv',
                '2025 QS WUR subject rankings Psychology.csv'
            ]
            
            for file in qs_files:
                try:
                    subject = file.replace('2025 QS WUR subject rankings ', '').replace('2025 QS WUR by Subject Overall in ', '').replace('.csv', '')
                    self.qs_data[subject] = pd.read_csv(file)
                    print(f"QS {subject} loaded: {len(self.qs_data[subject])} institutions")
                except FileNotFoundError:
                    print(f"File not found: {file}")
            
            # Load THE Subject Rankings 2025
            the_files = [
                '2025 THE WUR subject rankings Medical & Health.csv',
                '2025 THE WUR subject rankings Life Sciences.csv',
                '2025 THE WUR subject rankings Physical Sciences.csv',
                '2025 THE WUR subject rankings Psychology.csv'
            ]
            
            for file in the_files:
                try:
                    subject = file.replace('2025 THE WUR subject rankings ', '').replace('.csv', '')
                    self.the_data[subject] = pd.read_csv(file)
                    print(f"THE {subject} loaded: {len(self.the_data[subject])} institutions")
                except FileNotFoundError:
                    print(f"File not found: {file}")
                    
            return {
                'nirf_institutions': len(self.nirf_data) if self.nirf_data is not None else 0,
                'qs_subjects': len(self.qs_data),
                'the_subjects': len(self.the_data)
            }
            
        except Exception as e:
            print(f"Error loading data: {str(e)}")
            return None
    
    def analyze_nirf_performance(self):
        """
        Analyze NIRF 2024 Medical Category performance patterns.
        
        Returns
        -------
        dict
            Analysis results including top performers, parameter importance, and insights
        """
        if self.nirf_data is None:
            return None
            
        # Top 10 performers analysis
        top_10 = self.nirf_data.head(10).copy()
        
        # Parameter correlation analysis
        score_columns = [col for col in self.nirf_data.columns if 'SCORE_' in col]
        correlation_matrix = self.nirf_data[score_columns + ['Score']].corr()['Score'].sort_values(ascending=False)
        
        # Private vs Government analysis
        private_govt_analysis = self.nirf_data.groupby('Private/Government').agg({
            'Score': ['mean', 'median', 'std', 'count'],
            'Overall_Rank': ['mean', 'median']
        }).round(2)
        
        # State-wise performance
        state_performance = self.nirf_data.groupby('State').agg({
            'Score': ['mean', 'count'],
            'Overall_Rank': 'mean'
        }).sort_values(('Score', 'mean'), ascending=False)
        
        # Parameter importance analysis
        parameter_importance = {
            'TLR (Teaching-Learning Resources)': correlation_matrix.filter(regex='TLR').mean(),
            'RP (Research & Professional Practice)': correlation_matrix.filter(regex='RP').mean(),
            'GO (Graduation Outcomes)': correlation_matrix.filter(regex='GO').mean(),
            'OI (Outreach & Inclusivity)': correlation_matrix.filter(regex='OI').mean(),
            'PR (Perception)': correlation_matrix.get('SCORE_PR__(out_of_100)', 0)
        }
        
        self.analysis_results['nirf'] = {
            'top_performers': top_10,
            'parameter_correlations': correlation_matrix,
            'private_vs_govt': private_govt_analysis,
            'state_performance': state_performance,
            'parameter_importance': parameter_importance,
            'total_institutions': len(self.nirf_data)
        }
        
        return self.analysis_results['nirf']
    
    def analyze_qs_performance(self):
        """
        Analyze QS Subject Rankings 2025 performance patterns.
        
        Returns
        -------
        dict
            Analysis results for each QS subject area
        """
        qs_analysis = {}
        
        for subject, data in self.qs_data.items():
            if data is not None and len(data) > 0:
                # Indian institutions analysis
                indian_institutions = data[data['Country'] == 'India'].copy()
                
                # Top performers globally and in India
                global_top_10 = data.head(10) if len(data) >= 10 else data
                indian_top_5 = indian_institutions.head(5) if len(indian_institutions) >= 5 else indian_institutions
                
                # Parameter analysis for Indian institutions
                score_columns = [col for col in data.columns if 'Score' in col and col != 'Score']
                if len(score_columns) > 0 and len(indian_institutions) > 0:
                    indian_param_analysis = indian_institutions[score_columns].describe()
                else:
                    indian_param_analysis = None
                
                qs_analysis[subject] = {
                    'total_institutions': len(data),
                    'indian_institutions': len(indian_institutions),
                    'global_top_10': global_top_10,
                    'indian_top_5': indian_top_5,
                    'indian_parameter_analysis': indian_param_analysis
                }
        
        self.analysis_results['qs'] = qs_analysis
        return qs_analysis
    
    def analyze_the_performance(self):
        """
        Analyze THE Subject Rankings 2025 performance patterns.
        
        Returns
        -------
        dict
            Analysis results for each THE subject area
        """
        the_analysis = {}
        
        for subject, data in self.the_data.items():
            if data is not None and len(data) > 0:
                # Find SIU's position
                siu_position = data[data['Institution_Name'].str.contains('Symbiosis', case=False, na=False)]
                
                # Indian institutions analysis
                indian_institutions = data.dropna(subset=['Institution_Name'])
                
                # Parameter analysis
                param_columns = ['Research_Quality', 'Industry', 'International_Outlook', 
                               'Research_Enviroment', 'Teaching']
                available_params = [col for col in param_columns if col in data.columns]
                
                if len(available_params) > 0:
                    param_analysis = data[available_params].describe()
                    
                    # SIU parameter comparison if found
                    if len(siu_position) > 0:
                        siu_params = siu_position[available_params].iloc[0]
                        median_params = data[available_params].median()
                        siu_vs_median = siu_params - median_params
                    else:
                        siu_params = None
                        siu_vs_median = None
                else:
                    param_analysis = None
                    siu_params = None
                    siu_vs_median = None
                
                the_analysis[subject] = {
                    'total_institutions': len(data),
                    'siu_position': siu_position,
                    'parameter_analysis': param_analysis,
                    'siu_parameters': siu_params,
                    'siu_vs_median': siu_vs_median,
                    'top_10': data.head(10)
                }
        
        self.analysis_results['the'] = the_analysis
        return the_analysis
    
    def generate_key_insights(self):
        """
        Generate key strategic insights from all ranking analyses.
        
        Returns
        -------
        dict
            Comprehensive insights and recommendations
        """
        insights = {
            'current_position': {},
            'improvement_opportunities': {},
            'benchmarking_targets': {},
            'strategic_recommendations': {}
        }
        
        # Current Position Analysis
        if 'the' in self.analysis_results:
            medical_health = self.analysis_results['the'].get('Medical & Health', {})
            siu_position = medical_health.get('siu_position')
            if siu_position is not None and len(siu_position) > 0:
                insights['current_position']['the_medical_health'] = {
                    'rank': siu_position['Rank_2025'].iloc[0],
                    'overall_score': siu_position['Overall'].iloc[0],
                    'students': siu_position['No._of_FTE_Students'].iloc[0],
                    'international_students': siu_position['International_Students'].iloc[0]
                }
        
        # Improvement Opportunities
        if 'nirf' in self.analysis_results:
            param_importance = self.analysis_results['nirf']['parameter_importance']
            insights['improvement_opportunities']['nirf_focus_areas'] = {
                'highest_impact': max(param_importance, key=param_importance.get),
                'parameter_weights': param_importance
            }
        
        # Benchmarking Targets
        if 'qs' in self.analysis_results:
            medicine_data = self.analysis_results['qs'].get('Medicine', {})
            indian_top = medicine_data.get('indian_top_5')
            if indian_top is not None and len(indian_top) > 0:
                insights['benchmarking_targets']['qs_medicine'] = {
                    'top_indian_institutions': indian_top['Institution_Name'].tolist()[:3],
                    'target_scores': indian_top['Score'].tolist()[:3]
                }
        
        # Strategic Recommendations
        insights['strategic_recommendations'] = {
            'research_excellence': 'Focus on increasing research output and citations',
            'international_collaboration': 'Develop strategic international partnerships',
            'industry_engagement': 'Strengthen industry connections and practical training',
            'faculty_development': 'Recruit high-quality faculty and support research',
            'infrastructure_investment': 'Upgrade facilities and technology'
        }
        
        self.analysis_results['insights'] = insights
        return insights

if __name__ == "__main__":
    # Initialize analyzer and run comprehensive analysis
    analyzer = RankingsAnalyzer()
    
    # Load all data
    print("Loading ranking data...")
    load_results = analyzer.load_all_data()
    print(f"Data loading completed: {load_results}")
    
    # Run analyses
    print("\nAnalyzing NIRF performance...")
    nirf_results = analyzer.analyze_nirf_performance()
    
    print("Analyzing QS performance...")
    qs_results = analyzer.analyze_qs_performance()
    
    print("Analyzing THE performance...")
    the_results = analyzer.analyze_the_performance()
    
    print("Generating key insights...")
    insights = analyzer.generate_key_insights()
    
    print("\nAnalysis completed successfully!")
