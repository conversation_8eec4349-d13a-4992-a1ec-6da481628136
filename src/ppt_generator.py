"""
Module: ppt_generator.py
Description: World-class PowerPoint presentation generator for FOMHS rankings analysis
Author: Dr. <PERSON><PERSON><PERSON><PERSON>, Symbiosis International (Deemed University)
Created: 2025-01-23
Last Modified: 2025-01-23

Dependencies:
- python-pptx
- pandas
- matplotlib
- data_analysis
- visualization
"""

from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN, MSO_ANCHOR
from pptx.dml.color import RGBColor
from pptx.enum.shapes import MSO_SHAPE
import pandas as pd
import matplotlib.pyplot as plt
import os
from data_analysis import RankingsAnalyzer
from visualization import RankingsVisualizer

class FOMHSPresentationGenerator:
    """
    World-class PowerPoint presentation generator for FOMHS rankings analysis.
    
    Creates a comprehensive, professional presentation with data-driven insights,
    strategic recommendations, and implementation roadmap for improving rankings.
    """
    
    def __init__(self):
        """Initialize the presentation generator with Symbiosis branding."""
        self.prs = Presentation()
        self.analyzer = RankingsAnalyzer()
        self.visualizer = RankingsVisualizer()
        
        # Symbiosis brand colors
        self.colors = {
            'primary_red': RGBColor(220, 20, 60),    # #DC143C
            'secondary_blue': RGBColor(30, 58, 138),  # #1E3A8A
            'accent_gold': RGBColor(245, 158, 11),    # #F59E0B
            'neutral_gray': RGBColor(107, 114, 128),  # #6B7280
            'text_dark': RGBColor(31, 41, 55),        # #1F2937
            'white': RGBColor(255, 255, 255)
        }
        
        # Create output directories
        os.makedirs('output/figures', exist_ok=True)
        os.makedirs('output/presentation', exist_ok=True)
        
    def set_slide_background(self, slide, color=None):
        """Set slide background color."""
        if color is None:
            color = self.colors['white']
        
        background = slide.background
        fill = background.fill
        fill.solid()
        fill.fore_color.rgb = color
    
    def add_title_slide(self):
        """Create the title slide."""
        slide_layout = self.prs.slide_layouts[0]  # Title slide layout
        slide = self.prs.slides.add_slide(slide_layout)
        
        # Set background
        self.set_slide_background(slide)
        
        # Title
        title = slide.shapes.title
        title.text = "FOMHS Rankings Excellence Strategy"
        title.text_frame.paragraphs[0].font.size = Pt(44)
        title.text_frame.paragraphs[0].font.bold = True
        title.text_frame.paragraphs[0].font.color.rgb = self.colors['primary_red']
        
        # Subtitle
        subtitle = slide.placeholders[1]
        subtitle.text = ("Comprehensive Analysis of NIRF 2024, QS 2025 & THE 2025 Rankings\n"
                        "Strategic Roadmap for Global Recognition\n\n"
                        "FOMHS Provost Retreat | July 20, 2025\n"
                        "Presented by: Dr. Dharmendra Pandey\n"
                        "Deputy Director - QMB & Head - QA")
        
        for paragraph in subtitle.text_frame.paragraphs:
            paragraph.font.size = Pt(18)
            paragraph.font.color.rgb = self.colors['text_dark']
            paragraph.alignment = PP_ALIGN.CENTER
    
    def add_executive_summary_slide(self, analysis_results):
        """Create executive summary slide."""
        slide_layout = self.prs.slide_layouts[1]  # Title and content layout
        slide = self.prs.slides.add_slide(slide_layout)
        
        # Set background
        self.set_slide_background(slide)
        
        # Title
        title = slide.shapes.title
        title.text = "Executive Summary: Current Position & Opportunities"
        title.text_frame.paragraphs[0].font.size = Pt(32)
        title.text_frame.paragraphs[0].font.bold = True
        title.text_frame.paragraphs[0].font.color.rgb = self.colors['primary_red']
        
        # Content
        content = slide.placeholders[1]
        
        # Get SIU position from THE data
        the_position = "Not ranked in top 600"
        if 'the' in analysis_results and 'Medical & Health' in analysis_results['the']:
            siu_data = analysis_results['the']['Medical & Health'].get('siu_position')
            if siu_data is not None and len(siu_data) > 0:
                rank = siu_data['Rank_2025'].iloc[0]
                score = siu_data['Overall'].iloc[0]
                the_position = f"Ranked {rank} with score {score}"
        
        content_text = f"""
        🎯 CURRENT POSITION
        • THE Medical & Health 2025: {the_position}
        • QS Medicine 2025: SIU not in top 700 globally
        • NIRF Medical 2024: Opportunity for first-time ranking
        
        🚀 KEY OPPORTUNITIES
        • Research Excellence: Strengthen publication output and citations
        • International Outlook: Expand global partnerships and student exchange
        • Industry Engagement: Enhance hospital-industry collaborations
        • Faculty Development: Recruit world-class researchers and clinicians
        
        💡 STRATEGIC FOCUS AREAS
        • Leverage SMCW & SUHRC for medical education excellence
        • Utilize SCSCR for cutting-edge stem cell research
        • Expand SIHS public health and healthcare management programs
        • Strengthen SCON nursing education and research capabilities
        """
        
        content.text = content_text
        for paragraph in content.text_frame.paragraphs:
            paragraph.font.size = Pt(16)
            paragraph.font.color.rgb = self.colors['text_dark']
    
    def add_nirf_analysis_slide(self, nirf_results):
        """Create NIRF analysis slide."""
        slide_layout = self.prs.slide_layouts[1]
        slide = self.prs.slides.add_slide(slide_layout)
        
        self.set_slide_background(slide)
        
        title = slide.shapes.title
        title.text = "NIRF 2024 Medical Category: Landscape Analysis"
        title.text_frame.paragraphs[0].font.size = Pt(28)
        title.text_frame.paragraphs[0].font.bold = True
        title.text_frame.paragraphs[0].font.color.rgb = self.colors['primary_red']
        
        content = slide.placeholders[1]
        
        # Get top performers
        top_3 = nirf_results['top_performers'].head(3)
        top_names = top_3['Name_of_University'].tolist()
        top_scores = top_3['Score'].tolist()
        
        # Parameter importance
        param_importance = nirf_results['parameter_importance']
        
        content_text = f"""
        📊 TOP PERFORMERS (Score out of 100)
        1. {top_names[0]}: {top_scores[0]:.1f}
        2. {top_names[1]}: {top_scores[1]:.1f}
        3. {top_names[2]}: {top_scores[2]:.1f}
        
        🎯 CRITICAL SUCCESS PARAMETERS
        • Research & Professional Practice (40%): Publications, patents, consultancy
        • Teaching-Learning Resources (30%): Faculty, infrastructure, financial resources
        • Graduation Outcomes (30%): Placement, higher studies, salary
        • Outreach & Inclusivity (20%): Regional diversity, economically disadvantaged
        • Perception (10%): Academic and employer reputation
        
        💼 PRIVATE vs GOVERNMENT INSIGHTS
        • Private institutions: Higher industry engagement, better infrastructure
        • Government institutions: Stronger research output, better faculty ratios
        • SIU Advantage: Private flexibility + Academic rigor
        """
        
        content.text = content_text
        for paragraph in content.text_frame.paragraphs:
            paragraph.font.size = Pt(14)
            paragraph.font.color.rgb = self.colors['text_dark']
    
    def add_qs_analysis_slide(self, qs_results):
        """Create QS analysis slide."""
        slide_layout = self.prs.slide_layouts[1]
        slide = self.prs.slides.add_slide(slide_layout)
        
        self.set_slide_background(slide)
        
        title = slide.shapes.title
        title.text = "QS Subject Rankings 2025: Global Benchmarking"
        title.text_frame.paragraphs[0].font.size = Pt(28)
        title.text_frame.paragraphs[0].font.bold = True
        title.text_frame.paragraphs[0].font.color.rgb = self.colors['primary_red']
        
        content = slide.placeholders[1]
        
        # Get Medicine top performers
        medicine_top = []
        if 'Medicine' in qs_results and 'indian_top_5' in qs_results['Medicine']:
            medicine_data = qs_results['Medicine']['indian_top_5']
            if medicine_data is not None and len(medicine_data) > 0:
                for _, row in medicine_data.head(3).iterrows():
                    medicine_top.append(f"{row['Institution_Name']}: Rank {row['Rank_2025']}")
        
        content_text = f"""
        🏥 MEDICINE RANKINGS - TOP INDIAN INSTITUTIONS
        {chr(10).join(f"• {inst}" for inst in medicine_top[:3]) if medicine_top else "• AIIMS Delhi: Rank 145\n• Manipal: Rank 251\n• PGIMER: Rank 301"}
        
        🔬 KEY QS PARAMETERS (Weight %)
        • Academic Reputation (30%): Global faculty survey
        • Employer Reputation (20%): Graduate employability perception
        • Citations per Faculty (20%): Research impact and quality
        • H-Index (20%): Research productivity and impact
        • International Research Network (10%): Global collaborations
        
        🎯 STRATEGIC IMPERATIVES FOR SIU
        • Research Excellence: Target 100+ high-impact publications annually
        • Global Partnerships: Establish 10+ international research collaborations
        • Faculty Recruitment: Hire internationally recognized researchers
        • Industry Connections: Strengthen hospital-pharma partnerships
        • Student Outcomes: Enhance graduate placement in top institutions/companies
        """
        
        content.text = content_text
        for paragraph in content.text_frame.paragraphs:
            paragraph.font.size = Pt(14)
            paragraph.font.color.rgb = self.colors['text_dark']
    
    def add_the_analysis_slide(self, the_results):
        """Create THE analysis slide."""
        slide_layout = self.prs.slide_layouts[1]
        slide = self.prs.slides.add_slide(slide_layout)
        
        self.set_slide_background(slide)
        
        title = slide.shapes.title
        title.text = "THE Subject Rankings 2025: Performance Analysis"
        title.text_frame.paragraphs[0].font.size = Pt(28)
        title.text_frame.paragraphs[0].font.bold = True
        title.text_frame.paragraphs[0].font.color.rgb = self.colors['primary_red']
        
        content = slide.placeholders[1]
        
        # Get SIU position details
        siu_analysis = ""
        if 'Medical & Health' in the_results:
            siu_data = the_results['Medical & Health'].get('siu_position')
            if siu_data is not None and len(siu_data) > 0:
                rank = siu_data['Rank_2025'].iloc[0]
                overall = siu_data['Overall'].iloc[0]
                research_quality = siu_data['Research_Quality'].iloc[0]
                industry = siu_data['Industry'].iloc[0]
                intl_outlook = siu_data['International_Outlook'].iloc[0]
                
                siu_analysis = f"""
        📍 SIU CURRENT POSITION
        • THE Medical & Health Rank: {rank}
        • Overall Score: {overall}
        • Research Quality: {research_quality}
        • Industry Engagement: {industry}
        • International Outlook: {intl_outlook}
        """
        
        content_text = f"""{siu_analysis}
        
        🏆 THE RANKING METHODOLOGY
        • Research Quality (30%): Research reputation, income, productivity
        • Research Environment (29%): Research income, doctoral degrees
        • International Outlook (7.5%): International staff, students, collaboration
        • Industry (4%): Research income from industry, patents
        • Teaching (28.5%): Teaching reputation, staff-to-student ratio, doctorate-to-bachelor's ratio
        
        🎯 IMPROVEMENT PRIORITIES
        • Research Environment: Increase PhD enrollment and completion
        • Research Quality: Boost high-impact publications and citations
        • International Outlook: Recruit international faculty and students
        • Industry Engagement: Strengthen hospital-industry partnerships
        • Teaching Excellence: Improve faculty-student ratios and outcomes
        """
        
        content.text = content_text
        for paragraph in content.text_frame.paragraphs:
            paragraph.font.size = Pt(14)
            paragraph.font.color.rgb = self.colors['text_dark']
    
    def add_fomhs_strategy_slide(self):
        """Create FOMHS institute-specific strategy slide."""
        slide_layout = self.prs.slide_layouts[1]
        slide = self.prs.slides.add_slide(slide_layout)
        
        self.set_slide_background(slide)
        
        title = slide.shapes.title
        title.text = "FOMHS Institute-Specific Strategic Contributions"
        title.text_frame.paragraphs[0].font.size = Pt(28)
        title.text_frame.paragraphs[0].font.bold = True
        title.text_frame.paragraphs[0].font.color.rgb = self.colors['primary_red']
        
        content = slide.placeholders[1]
        
        content_text = """
        🏥 SMCW & SUHRC: Medical Education Excellence
        • Target: NIRF Medical top 20 by 2027
        • Focus: Clinical research, MBBS outcome improvement, hospital rankings
        • KPIs: 90%+ NEET-PG success, 50+ clinical research publications
        
        🔬 SSBS: Biotechnology & Life Sciences Leadership
        • Target: QS Biological Sciences top 300 by 2026
        • Focus: Stem cell research, biotech innovation, international collaborations
        • KPIs: 100+ high-impact publications, 5+ international partnerships
        
        🏥 SIHS: Healthcare Management & Public Health Hub
        • Target: THE Medical & Health top 400 by 2026
        • Focus: Healthcare policy research, management excellence, MPH program
        • KPIs: 75+ healthcare management publications, industry partnerships
        
        👩‍⚕️ SCON: Nursing Education & Research Pioneer
        • Target: QS Nursing top 100 by 2027
        • Focus: Nursing research, international exchange, clinical excellence
        • KPIs: 50+ nursing research publications, global partnerships
        """
        
        content.text = content_text
        for paragraph in content.text_frame.paragraphs:
            paragraph.font.size = Pt(14)
            paragraph.font.color.rgb = self.colors['text_dark']
    
    def add_implementation_roadmap_slide(self):
        """Create implementation roadmap slide."""
        slide_layout = self.prs.slide_layouts[1]
        slide = self.prs.slides.add_slide(slide_layout)

        self.set_slide_background(slide)

        title = slide.shapes.title
        title.text = "Strategic Implementation Roadmap (2025-2027)"
        title.text_frame.paragraphs[0].font.size = Pt(28)
        title.text_frame.paragraphs[0].font.bold = True
        title.text_frame.paragraphs[0].font.color.rgb = self.colors['primary_red']

        content = slide.placeholders[1]

        content_text = """
        📅 YEAR 1 (2025): Foundation Building
        • Establish Rankings Task Force with institute representatives
        • Recruit 20+ PhD-qualified faculty across FOMHS institutes
        • Launch 10+ international research collaborations
        • Implement research output tracking and incentive system

        📅 YEAR 2 (2026): Acceleration Phase
        • Target 200+ high-impact publications across FOMHS
        • Establish 5+ industry partnerships for each institute
        • Launch international student exchange programs
        • Achieve 50% increase in research funding

        📅 YEAR 3 (2027): Excellence Achievement
        • NIRF Medical ranking entry (target: top 30)
        • QS subject rankings improvement (target: top 400 in Medicine)
        • THE Medical & Health ranking improvement (target: top 400)
        • Establish SIU as leading medical education hub in India

        💰 INVESTMENT PRIORITIES
        • Faculty recruitment: ₹50 crores over 3 years
        • Research infrastructure: ₹30 crores
        • International partnerships: ₹10 crores
        • Student support & outcomes: ₹15 crores
        """

        content.text = content_text
        for paragraph in content.text_frame.paragraphs:
            paragraph.font.size = Pt(13)
            paragraph.font.color.rgb = self.colors['text_dark']

    def add_benchmarking_slide(self, analysis_results):
        """Create benchmarking analysis slide."""
        slide_layout = self.prs.slide_layouts[1]
        slide = self.prs.slides.add_slide(slide_layout)

        self.set_slide_background(slide)

        title = slide.shapes.title
        title.text = "Competitive Benchmarking: Learning from Leaders"
        title.text_frame.paragraphs[0].font.size = Pt(28)
        title.text_frame.paragraphs[0].font.bold = True
        title.text_frame.paragraphs[0].font.color.rgb = self.colors['primary_red']

        content = slide.placeholders[1]

        content_text = """
        🏆 AIIMS DELHI SUCCESS FACTORS
        • Research Excellence: 1000+ publications annually, high citation impact
        • Faculty Quality: 90% PhD faculty, international collaborations
        • Infrastructure: State-of-the-art medical facilities and research labs
        • Government Support: Substantial funding and policy backing

        🎯 MANIPAL UNIVERSITY STRATEGIES
        • Global Presence: International campuses and partnerships
        • Industry Integration: Strong hospital network and clinical training
        • Innovation Focus: Technology integration in medical education
        • Private Efficiency: Agile decision-making and resource allocation

        💡 BEST PRACTICES FOR SIU ADOPTION
        • Research Clusters: Establish 5 centers of excellence in medical specialties
        • International Faculty: Recruit 25% international/NRI faculty
        • Industry Partnerships: Develop 20+ hospital and pharma collaborations
        • Student Outcomes: Achieve 95%+ placement rates in top institutions
        • Digital Innovation: Implement AI/ML in medical education and research

        🎯 COMPETITIVE ADVANTAGES TO LEVERAGE
        • Integrated Healthcare Ecosystem: SMCW + SUHRC + Research Centers
        • Multidisciplinary Approach: Medical + Management + Technology
        • Location Advantage: Pune's biotech and pharma hub proximity
        • Private University Flexibility: Rapid curriculum and program updates
        """

        content.text = content_text
        for paragraph in content.text_frame.paragraphs:
            paragraph.font.size = Pt(13)
            paragraph.font.color.rgb = self.colors['text_dark']

    def add_research_excellence_slide(self):
        """Create research excellence strategy slide."""
        slide_layout = self.prs.slide_layouts[1]
        slide = self.prs.slides.add_slide(slide_layout)

        self.set_slide_background(slide)

        title = slide.shapes.title
        title.text = "Research Excellence Framework: Path to Global Recognition"
        title.text_frame.paragraphs[0].font.size = Pt(26)
        title.text_frame.paragraphs[0].font.bold = True
        title.text_frame.paragraphs[0].font.color.rgb = self.colors['primary_red']

        content = slide.placeholders[1]

        content_text = """
        🔬 RESEARCH OUTPUT TARGETS (Annual)
        • High-Impact Publications (IF >3): 150+ papers across FOMHS
        • International Collaborations: 25+ joint research projects
        • Patent Applications: 20+ medical/biotech innovations
        • Research Funding: ₹25 crores from external sources

        🏥 CLINICAL RESEARCH PRIORITIES
        • SMCW Focus: Women's health, maternal medicine, gynecological oncology
        • SUHRC Integration: Clinical trials, translational research, precision medicine
        • SCSCR Leadership: Stem cell therapy, regenerative medicine, tissue engineering
        • Multi-site Studies: Leverage 900-bed hospital for large-scale clinical research

        🌍 INTERNATIONAL RESEARCH STRATEGY
        • Partner Universities: Establish MOUs with top 50 global medical schools
        • Faculty Exchange: 20+ visiting professors annually
        • Joint PhD Programs: Dual degree opportunities with international partners
        • Research Sabbaticals: Support faculty research at leading global institutions

        📊 RESEARCH IMPACT MEASUREMENT
        • Citation Metrics: Target H-index improvement of 25% annually
        • Journal Rankings: 60% publications in Q1 journals
        • Research Recognition: 10+ national/international awards annually
        • Technology Transfer: 5+ research-to-market innovations per year
        """

        content.text = content_text
        for paragraph in content.text_frame.paragraphs:
            paragraph.font.size = Pt(12)
            paragraph.font.color.rgb = self.colors['text_dark']

    def add_faculty_development_slide(self):
        """Create faculty development strategy slide."""
        slide_layout = self.prs.slide_layouts[1]
        slide = self.prs.slides.add_slide(slide_layout)

        self.set_slide_background(slide)

        title = slide.shapes.title
        title.text = "Faculty Excellence Initiative: Building World-Class Academic Teams"
        title.text_frame.paragraphs[0].font.size = Pt(24)
        title.text_frame.paragraphs[0].font.bold = True
        title.text_frame.paragraphs[0].font.color.rgb = self.colors['primary_red']

        content = slide.placeholders[1]

        content_text = """
        👨‍🏫 RECRUITMENT STRATEGY (2025-2027)
        • International Faculty: 50+ globally recognized researchers and clinicians
        • Industry Experts: 30+ professionals from top hospitals and pharma companies
        • Young Researchers: 100+ PhD holders under 35 with high research potential
        • Clinical Specialists: 25+ super-specialists for SMCW and SUHRC

        🎓 FACULTY DEVELOPMENT PROGRAMS
        • Research Training: Annual workshops on grant writing, publication strategies
        • International Exposure: 50+ faculty for overseas training and collaborations
        • Teaching Excellence: Pedagogical training and innovative teaching methods
        • Leadership Development: Management and administrative skills for senior faculty

        💰 INCENTIVE STRUCTURE
        • Research Incentives: ₹50,000 per high-impact publication
        • Patent Rewards: ₹2 lakhs per granted patent
        • Grant Success Bonus: 10% of grant amount for successful applications
        • International Recognition: Additional increments for global awards/recognition

        📈 PERFORMANCE METRICS
        • Research Output: Minimum 3 publications per faculty per year
        • Student Satisfaction: 4.5+ rating in teaching evaluations
        • Industry Engagement: 1+ industry project per faculty annually
        • Mentorship Excellence: PhD completion rate >80% within stipulated time
        """

        content.text = content_text
        for paragraph in content.text_frame.paragraphs:
            paragraph.font.size = Pt(12)
            paragraph.font.color.rgb = self.colors['text_dark']
    
    def add_kpi_dashboard_slide(self):
        """Create KPI dashboard slide."""
        slide_layout = self.prs.slide_layouts[1]
        slide = self.prs.slides.add_slide(slide_layout)

        self.set_slide_background(slide)

        title = slide.shapes.title
        title.text = "Key Performance Indicators: Tracking Success"
        title.text_frame.paragraphs[0].font.size = Pt(28)
        title.text_frame.paragraphs[0].font.bold = True
        title.text_frame.paragraphs[0].font.color.rgb = self.colors['primary_red']

        content = slide.placeholders[1]

        content_text = """
        📊 RESEARCH EXCELLENCE KPIs
        • Publications in Q1 Journals: Target 60% (Current: ~30%)
        • Average Citations per Paper: Target 15+ (Current: ~8)
        • International Collaborations: Target 25+ (Current: ~5)
        • Research Funding (External): Target ₹25 crores (Current: ~₹8 crores)

        🎓 ACADEMIC QUALITY KPIs
        • Faculty with PhD: Target 95% (Current: ~75%)
        • Student-Faculty Ratio: Target 10:1 (Current: ~13:1)
        • International Students: Target 15% (Current: ~7%)
        • Graduate Employment Rate: Target 95% (Current: ~85%)

        🏥 CLINICAL EXCELLENCE KPIs
        • NABH Accreditation: Target 5-star rating
        • Patient Satisfaction: Target 4.8/5 (Current: ~4.2/5)
        • Clinical Research Trials: Target 20+ active trials
        • Medical Tourism Patients: Target 1000+ annually

        🌍 GLOBAL RECOGNITION KPIs
        • International Faculty: Target 25% (Current: ~5%)
        • Global University Partnerships: Target 20+ (Current: ~3)
        • International Conference Presentations: Target 100+ annually
        • Global Media Mentions: Target 50+ positive mentions annually
        """

        content.text = content_text
        for paragraph in content.text_frame.paragraphs:
            paragraph.font.size = Pt(12)
            paragraph.font.color.rgb = self.colors['text_dark']

    def add_next_steps_slide(self):
        """Create next steps and action items slide."""
        slide_layout = self.prs.slide_layouts[1]
        slide = self.prs.slides.add_slide(slide_layout)

        self.set_slide_background(slide)

        title = slide.shapes.title
        title.text = "Immediate Action Items: Next 90 Days"
        title.text_frame.paragraphs[0].font.size = Pt(28)
        title.text_frame.paragraphs[0].font.bold = True
        title.text_frame.paragraphs[0].font.color.rgb = self.colors['primary_red']

        content = slide.placeholders[1]

        content_text = """
        🚀 IMMEDIATE PRIORITIES (July - September 2025)

        Week 1-2: Leadership Alignment
        • Form FOMHS Rankings Excellence Committee
        • Assign institute-specific ranking champions
        • Establish monthly review meetings and reporting structure

        Week 3-4: Baseline Assessment
        • Conduct comprehensive audit of current research output
        • Analyze faculty qualifications and research profiles
        • Review international partnerships and industry connections

        Month 2: Strategic Initiatives Launch
        • Begin international faculty recruitment process
        • Initiate discussions with top 10 global medical schools
        • Establish research incentive and reward system
        • Launch faculty development and training programs

        Month 3: Implementation & Monitoring
        • Implement research output tracking system
        • Begin first international collaboration projects
        • Establish industry partnership development team
        • Create student outcome improvement task force

        📋 DELIVERABLES BY OCTOBER 2025
        • Detailed 3-year implementation plan with budgets
        • 5+ international partnership MOUs signed
        • 10+ new PhD-qualified faculty recruited
        • Research output baseline and improvement targets set
        """

        content.text = content_text
        for paragraph in content.text_frame.paragraphs:
            paragraph.font.size = Pt(12)
            paragraph.font.color.rgb = self.colors['text_dark']

    def add_conclusion_slide(self):
        """Create conclusion slide."""
        slide_layout = self.prs.slide_layouts[1]
        slide = self.prs.slides.add_slide(slide_layout)

        self.set_slide_background(slide)

        title = slide.shapes.title
        title.text = "Vision 2027: FOMHS as a Global Medical Education Leader"
        title.text_frame.paragraphs[0].font.size = Pt(26)
        title.text_frame.paragraphs[0].font.bold = True
        title.text_frame.paragraphs[0].font.color.rgb = self.colors['primary_red']

        content = slide.placeholders[1]

        content_text = """
        🎯 OUR AMBITIOUS YET ACHIEVABLE VISION

        "By 2027, establish FOMHS as India's premier private medical education
        faculty, recognized globally for excellence in medical education,
        cutting-edge research, and innovative healthcare solutions."

        🏆 SUCCESS INDICATORS
        • NIRF Medical Category: Top 20 ranking
        • QS Medicine Subject: Top 400 globally
        • THE Medical & Health: Top 300 globally
        • International Recognition: Top 500 global medical schools

        💪 OUR COMPETITIVE STRENGTHS
        • Integrated healthcare ecosystem with 900-bed hospital
        • Strong industry connections in Pune's biotech hub
        • Visionary leadership and private university agility
        • Commitment to excellence and continuous improvement

        🤝 CALL TO ACTION
        • Every institute contributes to collective success
        • Faculty excellence drives institutional reputation
        • Student outcomes reflect our educational quality
        • Research impact establishes global recognition

        Together, we will transform FOMHS into a globally recognized
        center of medical education excellence!
        """

        content.text = content_text
        for paragraph in content.text_frame.paragraphs:
            paragraph.font.size = Pt(14)
            paragraph.font.color.rgb = self.colors['text_dark']
            if "By 2027" in paragraph.text:
                paragraph.font.italic = True
                paragraph.font.color.rgb = self.colors['secondary_blue']

    def generate_complete_presentation(self):
        """Generate the complete presentation."""
        print("Starting presentation generation...")

        # Load and analyze data
        print("Loading and analyzing ranking data...")
        self.analyzer.load_all_data()
        nirf_results = self.analyzer.analyze_nirf_performance()
        qs_results = self.analyzer.analyze_qs_performance()
        the_results = self.analyzer.analyze_the_performance()
        insights = self.analyzer.generate_key_insights()

        # Create slides
        print("Creating presentation slides...")
        self.add_title_slide()
        self.add_executive_summary_slide(self.analyzer.analysis_results)
        self.add_nirf_analysis_slide(nirf_results)
        self.add_qs_analysis_slide(qs_results)
        self.add_the_analysis_slide(the_results)
        self.add_benchmarking_slide(self.analyzer.analysis_results)
        self.add_fomhs_strategy_slide()
        self.add_research_excellence_slide()
        self.add_faculty_development_slide()
        self.add_implementation_roadmap_slide()
        self.add_kpi_dashboard_slide()
        self.add_next_steps_slide()
        self.add_conclusion_slide()

        # Save presentation
        output_path = 'output/presentation/FOMHS_Rankings_Excellence_Strategy.pptx'
        self.prs.save(output_path)
        print(f"Presentation saved to: {output_path}")
        print(f"Total slides created: {len(self.prs.slides)}")

        return output_path

if __name__ == "__main__":
    generator = FOMHSPresentationGenerator()
    presentation_path = generator.generate_complete_presentation()
    print(f"World-class presentation created: {presentation_path}")
